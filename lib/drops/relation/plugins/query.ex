defmodule Drops.Relation.Plugins.Query do
  use Drops.Relation.Plugin

  defmacro query(source, bindings, expression) do
    # Parse the bindings - support both Ecto-style [u] and simple :u
    binding_vars = parse_bindings(bindings)

    # Transform the expression using the binding variables
    transformed_expr = transform_expression(expression, binding_vars, source)

    quote do
      unquote(transformed_expr)
    end
  end

  def on(:before_compiler, _relation, _attributes) do
  end

  # Parse bindings to extract variable names
  # Only supports Ecto-style [u] where u is a variable
  defp parse_bindings([{var, _, context}]) when is_atom(var) and is_atom(context) do
    # Ecto-style binding like [u] where u is a variable
    [{var, 0}]
  end

  defp parse_bindings(bindings) when is_list(bindings) do
    # Multiple bindings - assign positions
    bindings
    |> Enum.with_index()
    |> Enum.map(fn
      {{var, _, context}, index} when is_atom(var) and is_atom(context) ->
        {var, index}
    end)
  end

  # Transform expressions by replacing binding variables with the actual source
  defp transform_expression({:or, _meta, [left, right]}, binding_vars, source) do
    # Transform both sides of the OR
    left_transformed = transform_expression(left, binding_vars, source)
    right_transformed = transform_expression(right, binding_vars, source)

    # Create an OR operation
    quote do
      Drops.Relation.Operations.Or.new(
        unquote(left_transformed),
        unquote(right_transformed),
        unquote(source)
      )
    end
  end

  defp transform_expression({:and, _meta, [left, right]}, binding_vars, source) do
    # Transform both sides of the AND
    left_transformed = transform_expression(left, binding_vars, source)
    right_transformed = transform_expression(right, binding_vars, source)

    # Create an AND operation
    quote do
      Drops.Relation.Operations.And.new(
        unquote(left_transformed),
        unquote(right_transformed),
        unquote(source)
      )
    end
  end

  defp transform_expression(
         {{:., dot_meta, [{var, var_meta, context}, function_name]}, call_meta, args},
         binding_vars,
         source
       )
       when is_atom(var) and is_atom(context) and is_atom(function_name) do
    # Check if this is a call on one of our binding variables
    case find_binding_var(var, binding_vars) do
      {:ok, _position} ->
        # Replace the binding variable with the source
        quote do
          unquote(source).unquote(function_name)(unquote_splicing(args))
        end

      :error ->
        # Not one of our binding variables, leave as is
        {{:., dot_meta, [{var, var_meta, context}, function_name]}, call_meta, args}
    end
  end

  defp transform_expression(expr, _binding_vars, _source) do
    # For any other expression, return as is
    expr
  end

  # Find a binding variable in the list
  defp find_binding_var(var, binding_vars) do
    case Enum.find(binding_vars, fn {binding_var, _pos} -> binding_var == var end) do
      {_var, position} -> {:ok, position}
      nil -> :error
    end
  end
end
