defmodule Drops.Relations.Plugins.QueryTest do
  use Drops.RelationCase, async: false

  describe "query composition" do
    relation(:users) do
      schema("users", infer: true)
    end

    import Drops.Relation.Plugins.Query, only: [query: 3]

    test "query functions work with Ecto-style variable bindings", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      result =
        users
        |> query(
          [u],
          u.get_by_name("<PERSON>") or u.get_by_name("<PERSON>") or
            (u.restrict(active: true) and u.restrict(name: "<PERSON>"))
        )
        |> users.order(:name)
        |> Enum.to_list()

      assert [%{name: "<PERSON>"}, %{name: "<PERSON>"}] = result
    end

    test "AND operation works correctly", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      # Test AND operation that should match
      result =
        users
        |> query([u], u.restrict(active: true) and u.restrict(name: "<PERSON>"))
        |> Enum.to_list()

      assert [%{name: "Jane", active: true}] = result

      # Test AND operation that should not match
      result =
        users
        |> query([u], u.restrict(active: true) and u.restrict(name: "John"))
        |> Enum.to_list()

      assert [] = result
    end

    test "complex AND/OR combinations work correctly", %{users: users} do
      users.insert(%{name: "John", active: false})
      users.insert(%{name: "Jane", active: true})
      users.insert(%{name: "Joe", active: false})
      users.insert(%{name: "Jade", active: true})

      # Test: (active AND name=Jane) OR (active AND name=Jade)
      result =
        users
        |> query(
          [u],
          (u.restrict(active: true) and u.restrict(name: "Jane")) or
            (u.restrict(active: true) and u.restrict(name: "Jade"))
        )
        |> users.order(:name)
        |> Enum.to_list()

      assert [%{name: "Jade"}, %{name: "Jane"}] = result
    end
  end
end
